// src/controllers/adminDashboardController.js
const prisma = require("../utils/prisma");

class AdminDashboardController {
  /**
   * Determina o filtro de empresa com base no usuário, parâmetros de consulta e modelo
   * @param {Object} req - Objeto de requisição Express
   * @param {string} model - Nome do modelo Prisma (opcional)
   * @returns {Object} Filtro de empresa para consultas Prisma
   */
  static getCompanyFilter(req, model = null) {
    // Verificar se o modelo suporta filtragem por companyId
    const modelsWithoutCompanyId = ['client'];

    // Se o modelo não suporta companyId ou não está especificado, retorna um filtro vazio
    if (model && modelsWithoutCompanyId.includes(model.toLowerCase())) {
      return {};
    }

    // Se o usuário for SYSTEM_ADMIN e um companyId específico for fornecido na query
    if (req.user.role === "SYSTEM_ADMIN" && req.query.companyId) {
      return { companyId: req.query.companyId };
    }

    // Para usuários não-admin ou sem companyId específico na query
    if (req.user.role !== "SYSTEM_ADMIN" && req.user.companyId) {
      return { companyId: req.user.companyId };
    }

    // Se for SYSTEM_ADMIN sem companyId específico, retorna filtro vazio
    return {};
  }

  /**
   * Obter estatísticas gerais para o dashboard
   */
  static async getStats(req, res) {
    try {
      // Obter filtro de empresa com base no usuário e parâmetros
      const userCompanyFilter = AdminDashboardController.getCompanyFilter(req);

      // Obter contagem de usuários total
      const usersTotal = await prisma.user.count({
        where: {
          ...userCompanyFilter,
          active: true,
        },
      });

      // Obter contagem de usuários ativos (logados nas últimas 24h)
      const oneDayAgo = new Date();
      oneDayAgo.setDate(oneDayAgo.getDate() - 1);

      const activeUsers = await prisma.user.count({
        where: {
          ...userCompanyFilter,
          active: true,
          lastLoginAt: {
            gte: oneDayAgo,
          },
        },
      });

      // Cliente não tem companyId, usamos um filtro vazio
      const clientsTotal = await prisma.client.count({
        where: {
          active: true,
        },
      });

      // Obter contagem de agendamentos
      const appointmentsTotal = await prisma.scheduling.count({
        where: {
          ...userCompanyFilter,
        },
      });

      // Calcular percentuais de crescimento com base em dados reais
      // Calcular crescimento de usuários comparando com o mês anterior
      const oneMonthAgo = new Date();
      oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);

      const twoMonthsAgo = new Date();
      twoMonthsAgo.setMonth(twoMonthsAgo.getMonth() - 2);

      // Contagem de usuários do mês passado
      const lastMonthUsers = await prisma.user.count({
        where: {
          ...userCompanyFilter,
          active: true,
          createdAt: {
            gte: twoMonthsAgo,
            lt: oneMonthAgo,
          },
        },
      });

      // Contagem de usuários deste mês
      const thisMonthUsers = await prisma.user.count({
        where: {
          ...userCompanyFilter,
          active: true,
          createdAt: {
            gte: oneMonthAgo,
          },
        },
      });

      // Calcular crescimento de usuários ativos
      const lastMonthActiveUsers = await prisma.user.count({
        where: {
          ...userCompanyFilter,
          active: true,
          lastLoginAt: {
            gte: twoMonthsAgo,
            lt: oneMonthAgo,
          },
        },
      });

      // Cliente não tem companyId, usamos um filtro vazio
      const lastMonthClients = await prisma.client.count({
        where: {
          active: true,
          createdAt: {
            gte: twoMonthsAgo,
            lt: oneMonthAgo,
          },
        },
      });

      // Cliente não tem companyId, usamos um filtro vazio
      const thisMonthClients = await prisma.client.count({
        where: {
          active: true,
          createdAt: {
            gte: oneMonthAgo,
          },
        },
      });

      // Contagem de agendamentos do mês passado
      const lastMonthAppointments = await prisma.scheduling.count({
        where: {
          ...userCompanyFilter,
          createdAt: {
            gte: twoMonthsAgo,
            lt: oneMonthAgo,
          },
        },
      });

      // Contagem de agendamentos deste mês
      const thisMonthAppointments = await prisma.scheduling.count({
        where: {
          ...userCompanyFilter,
          createdAt: {
            gte: oneMonthAgo,
          },
        },
      });

      // Calcular percentuais de crescimento
      const calculateGrowth = (current, previous) => {
        if (previous === 0) return current > 0 ? 100 : 0;
        return ((current - previous) / previous) * 100;
      };

      const growthStats = {
        usersGrowth: parseFloat(calculateGrowth(thisMonthUsers, lastMonthUsers).toFixed(1)),
        activeUsersGrowth: parseFloat(calculateGrowth(activeUsers, lastMonthActiveUsers).toFixed(1)),
        clientsGrowth: parseFloat(calculateGrowth(thisMonthClients, lastMonthClients).toFixed(1)),
        appointmentsGrowth: parseFloat(calculateGrowth(thisMonthAppointments, lastMonthAppointments).toFixed(1)),
      };

      res.json({
        stats: {
          usersTotal,
          activeUsers,
          clientsTotal,
          appointmentsTotal,
        },
        growth: growthStats,
      });
    } catch (error) {
      console.error("Erro ao obter estatísticas:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Obter dados de atividade para gráficos
   */
  static async getActivityData(req, res) {
    try {
      const activityData = await AdminDashboardController.getActivityDataMethod(req);
      res.json({ activityData });
    } catch (error) {
      console.error("Erro ao obter dados de atividade:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Obter distribuição de usuários por módulo
   */
  static async getUserModuleDistribution(req, res) {
    try {
      const moduleDistribution = await AdminDashboardController.getModuleDistributionData(req);
      res.json({ moduleDistribution });
    } catch (error) {
      console.error("Erro ao obter distribuição por módulo:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Obter lista de usuários ativos
   */
  static async getActiveUsers(req, res) {
    try {
      const users = await AdminDashboardController.getActiveUsersData(req);
      res.json({ users });
    } catch (error) {
      console.error("Erro ao obter usuários ativos:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Obter atividades recentes
   */
  static async getRecentActivity(req, res) {
    try {
      const activities = await AdminDashboardController.getRecentActivityData(req);
      res.json({ activities });
    } catch (error) {
      console.error("Erro ao obter atividades recentes:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Obter informações do sistema
   */
  static async getSystemInfo(req, res) {
    try {
      const systemInfo = await AdminDashboardController.getSystemInfoData(req);
      res.json({ systemInfo });
    } catch (error) {
      console.error("Erro ao obter informações do sistema:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Obter distribuição de profissões por grupo
   */
  static async getProfessionDistribution(req, res) {
    try {
      const professionDistribution = await AdminDashboardController.getProfessionDistributionData(req);
      res.json({ professionDistribution });
    } catch (error) {
      console.error("Erro ao obter distribuição de profissões:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Obter todos os dados do dashboard em uma única requisição
   */
  static async getAllDashboardData(req, res) {
    try {
      // Executar todas as consultas em paralelo para melhor performance
      const [statsData, growth, activityData, moduleData, usersData, activitiesData, systemInfo, professionDistribution] =
        await Promise.all([
          AdminDashboardController.getStatsData(req),
          AdminDashboardController.getGrowthData(req),
          AdminDashboardController.getActivityDataMethod(req),
          AdminDashboardController.getModuleDistributionData(req),
          AdminDashboardController.getActiveUsersData(req),
          AdminDashboardController.getRecentActivityData(req),
          AdminDashboardController.getSystemInfoData(req),
          AdminDashboardController.getProfessionDistributionData(req),
        ]);

      const responseData = {
        stats: statsData,
        growth,
        activityData,
        userModuleData: moduleData,
        usersData,
        recentActivity: activitiesData,
        systemInfo,
        professionDistribution,
      };

      res.json(responseData);
    } catch (error) {
      console.error("Erro ao obter dados do dashboard:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  // Métodos auxiliares para obter os dados sem enviar resposta HTTP
  // Usados pelo método getAllDashboardData

  static async getStatsData(req) {
    const userCompanyFilter = AdminDashboardController.getCompanyFilter(req);

    const usersTotal = await prisma.user.count({
      where: {
        ...userCompanyFilter,
        active: true,
      },
    });

    const oneDayAgo = new Date();
    oneDayAgo.setDate(oneDayAgo.getDate() - 1);

    const activeUsers = await prisma.user.count({
      where: {
        ...userCompanyFilter,
        active: true,
        lastLoginAt: {
          gte: oneDayAgo,
        },
      },
    });

    // Cliente não tem companyId, usamos um filtro vazio
    const clientsTotal = await prisma.client.count({
      where: {
        active: true,
      },
    });

    const appointmentsTotal = await prisma.scheduling.count({
      where: {
        ...userCompanyFilter,
      },
    });

    // Para profissões e grupos, queremos ver todos os dados independentemente da empresa
    // quando o usuário for SYSTEM_ADMIN
    let professionWhere = {
      active: true,
      deletedAt: null,
    };

    let groupWhere = {
      active: true,
      deletedAt: null,
    };

    // Se o usuário não for SYSTEM_ADMIN, aplicar filtro de empresa
    if (req.user.role !== "SYSTEM_ADMIN" && req.user.companyId) {
      professionWhere.companyId = req.user.companyId;
      groupWhere.companyId = req.user.companyId;
    }

    // Se for SYSTEM_ADMIN e um companyId específico for fornecido na query
    if (req.user.role === "SYSTEM_ADMIN" && req.query.companyId) {
      professionWhere.companyId = req.query.companyId;
      groupWhere.companyId = req.query.companyId;
    }

    // Obter contagem de profissões
    const professionsTotal = await prisma.profession.count({
      where: professionWhere
    });

    // Obter contagem de grupos de profissões
    const groupsTotal = await prisma.professionGroup.count({
      where: groupWhere
    });

    return {
      usersTotal,
      activeUsers,
      clientsTotal,
      appointmentsTotal,
      professionsTotal,
      groupsTotal,
    };
  }

  static async getGrowthData(req) {
    const userCompanyFilter = AdminDashboardController.getCompanyFilter(req);

    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);

    const twoMonthsAgo = new Date();
    twoMonthsAgo.setMonth(twoMonthsAgo.getMonth() - 2);

    // Contagem de usuários do mês passado
    const lastMonthUsers = await prisma.user.count({
      where: {
        ...userCompanyFilter,
        active: true,
        createdAt: {
          gte: twoMonthsAgo,
          lt: oneMonthAgo,
        },
      },
    });

    // Contagem de usuários deste mês
    const thisMonthUsers = await prisma.user.count({
      where: {
        ...userCompanyFilter,
        active: true,
        createdAt: {
          gte: oneMonthAgo,
        },
      },
    });

    // Obter contagem de usuários ativos (logados nas últimas 24h)
    const oneDayAgo = new Date();
    oneDayAgo.setDate(oneDayAgo.getDate() - 1);

    const activeUsers = await prisma.user.count({
      where: {
        ...userCompanyFilter,
        active: true,
        lastLoginAt: {
          gte: oneDayAgo,
        },
      },
    });

    // Calcular crescimento de usuários ativos
    const lastMonthActiveUsers = await prisma.user.count({
      where: {
        ...userCompanyFilter,
        active: true,
        lastLoginAt: {
          gte: twoMonthsAgo,
          lt: oneMonthAgo,
        },
      },
    });

    // Cliente não tem companyId, usamos um filtro vazio
    const lastMonthClients = await prisma.client.count({
      where: {
        active: true,
        createdAt: {
          gte: twoMonthsAgo,
          lt: oneMonthAgo,
        },
      },
    });

    // Cliente não tem companyId, usamos um filtro vazio
    const thisMonthClients = await prisma.client.count({
      where: {
        active: true,
        createdAt: {
          gte: oneMonthAgo,
        },
      },
    });

    // Contagem de agendamentos do mês passado
    const lastMonthAppointments = await prisma.scheduling.count({
      where: {
        ...userCompanyFilter,
        createdAt: {
          gte: twoMonthsAgo,
          lt: oneMonthAgo,
        },
      },
    });

    // Contagem de agendamentos deste mês
    const thisMonthAppointments = await prisma.scheduling.count({
      where: {
        ...userCompanyFilter,
        createdAt: {
          gte: oneMonthAgo,
        },
      },
    });

    // Para profissões e grupos, queremos ver todos os dados independentemente da empresa
    // quando o usuário for SYSTEM_ADMIN
    let professionWhere = {
      active: true,
      deletedAt: null,
    };

    let groupWhere = {
      active: true,
      deletedAt: null,
    };

    // Se o usuário não for SYSTEM_ADMIN, aplicar filtro de empresa
    if (req.user.role !== "SYSTEM_ADMIN" && req.user.companyId) {
      professionWhere.companyId = req.user.companyId;
      groupWhere.companyId = req.user.companyId;
    }

    // Se for SYSTEM_ADMIN e um companyId específico for fornecido na query
    if (req.user.role === "SYSTEM_ADMIN" && req.query.companyId) {
      professionWhere.companyId = req.query.companyId;
      groupWhere.companyId = req.query.companyId;
    }

    // Contagem de profissões do mês passado
    const lastMonthProfessions = await prisma.profession.count({
      where: {
        ...professionWhere,
        createdAt: {
          gte: twoMonthsAgo,
          lt: oneMonthAgo,
        },
      },
    });

    // Contagem de profissões deste mês
    const thisMonthProfessions = await prisma.profession.count({
      where: {
        ...professionWhere,
        createdAt: {
          gte: oneMonthAgo,
        },
      },
    });

    // Contagem de grupos de profissões do mês passado
    const lastMonthGroups = await prisma.professionGroup.count({
      where: {
        ...groupWhere,
        createdAt: {
          gte: twoMonthsAgo,
          lt: oneMonthAgo,
        },
      },
    });

    // Contagem de grupos de profissões deste mês
    const thisMonthGroups = await prisma.professionGroup.count({
      where: {
        ...groupWhere,
        createdAt: {
          gte: oneMonthAgo,
        },
      },
    });

    // Calcular percentuais de crescimento
    const calculateGrowth = (current, previous) => {
      if (previous === 0) return current > 0 ? 100 : 0;
      return ((current - previous) / previous) * 100;
    };

    return {
      usersGrowth: parseFloat(calculateGrowth(thisMonthUsers, lastMonthUsers).toFixed(1)),
      activeUsersGrowth: parseFloat(calculateGrowth(activeUsers, lastMonthActiveUsers).toFixed(1)),
      clientsGrowth: parseFloat(calculateGrowth(thisMonthClients, lastMonthClients).toFixed(1)),
      appointmentsGrowth: parseFloat(calculateGrowth(thisMonthAppointments, lastMonthAppointments).toFixed(1)),
      professionsGrowth: parseFloat(calculateGrowth(thisMonthProfessions, lastMonthProfessions).toFixed(1)),
      groupsGrowth: parseFloat(calculateGrowth(thisMonthGroups, lastMonthGroups).toFixed(1)),
    };
  }

  static async getActivityDataMethod(req) {
    const { period = "7dias" } = req.query;
    const userCompanyFilter = AdminDashboardController.getCompanyFilter(req);

    // Determinar o intervalo de datas com base no período
    let startDate = new Date();
    switch (period) {
      case "30dias":
        startDate.setDate(startDate.getDate() - 30);
        break;
      case "3meses":
        startDate.setMonth(startDate.getMonth() - 3);
        break;
      default: // 7dias
        startDate.setDate(startDate.getDate() - 7);
    }

    // Coletar dados por dia usando agregações do Prisma
    // Para usuários - novos usuários por dia
    const usersData = await this.getAggregatedDataByDay(
      startDate,
      'user',
      'createdAt',
      userCompanyFilter
    );

    // Para clientes - novos clientes por dia (sem filtro de empresa)
    const clientsData = await this.getAggregatedDataByDay(
      startDate,
      'client',
      'createdAt',
      {} // Não usamos filtro de companyId para clientes
    );

    // Para agendamentos - agendamentos por dia
    const schedulingData = await this.getAggregatedDataByDay(
      startDate,
      'scheduling',
      'startDate',
      userCompanyFilter
    );

    // Processar os resultados em um formato adequado para o gráfico
    const daysOfWeek = ["Dom", "Seg", "Ter", "Qua", "Qui", "Sex", "Sáb"];

    // Criar um mapa de datas para facilitar a consolidação
    const dateMap = new Map();

    // Inicializar o mapa com todas as datas no intervalo
    let currentDate = new Date(startDate);
    const endDate = new Date();

    while (currentDate <= endDate) {
      const dateStr = this.formatDate(currentDate);
      const dayOfWeek = daysOfWeek[currentDate.getDay()];

      dateMap.set(dateStr, {
        date: dateStr,
        name: dayOfWeek,
        usuarios: 0,
        clientes: 0,
        agendamentos: 0,
      });

      currentDate.setDate(currentDate.getDate() + 1);
    }

    // Preencher dados de usuários
    usersData.forEach(item => {
      const dateStr = this.formatDate(new Date(item.date));
      if (dateMap.has(dateStr)) {
        const data = dateMap.get(dateStr);
        data.usuarios = item.count;
      }
    });

    // Preencher dados de clientes
    clientsData.forEach(item => {
      const dateStr = this.formatDate(new Date(item.date));
      if (dateMap.has(dateStr)) {
        const data = dateMap.get(dateStr);
        data.clientes = item.count;
      }
    });

    // Preencher dados de agendamentos
    schedulingData.forEach(item => {
      const dateStr = this.formatDate(new Date(item.date));
      if (dateMap.has(dateStr)) {
        const data = dateMap.get(dateStr);
        data.agendamentos = item.count;
      }
    });

    // Converter o mapa para um array
    const result = [];
    dateMap.forEach(value => {
      result.push(value);
    });

    return result;
  }

  /**
   * Método auxiliar para obter dados agregados por dia para diferentes modelos
   */
  static async getAggregatedDataByDay(startDate, model, dateField, companyFilter) {
    // Construir query dinâmica com base no modelo
    const where = {
      [dateField]: {
        gte: startDate
      }
    };

    // Adicionar filtro de empresa apenas se o modelo suportar
    if (Object.keys(companyFilter).length > 0 && model !== 'client') {
      Object.assign(where, companyFilter);
    }

    // Obter todos os registros no período
    const records = await prisma[model].findMany({
      where,
      select: {
        [dateField]: true
      }
    });

    // Agrupar por dia
    const groupedByDay = records.reduce((acc, record) => {
      const date = new Date(record[dateField]);
      const dateStr = this.formatDate(date);

      if (!acc[dateStr]) {
        acc[dateStr] = 0;
      }

      acc[dateStr]++;
      return acc;
    }, {});

    // Converter para array no formato esperado
    return Object.entries(groupedByDay).map(([date, count]) => ({
      date,
      count
    }));
  }

  static formatDate(date) {
    return date.toISOString().split('T')[0];
  }

  static async getModuleDistributionData(req) {
    const userCompanyFilter = AdminDashboardController.getCompanyFilter(req);

    // Consulta para obter a contagem de usuários por módulo
    const moduleCounts = await prisma.user.groupBy({
      by: ['modules'],
      where: {
        ...userCompanyFilter,
        active: true,
      },
      _count: {
        id: true,
      },
    });

    // Processar os resultados para o formato esperado pelo frontend
    const moduleMap = new Map();

    // Inicializar com todos os módulos para garantir que todos apareçam
    moduleMap.set('ADMIN', { name: 'Admin', value: 0 });
    moduleMap.set('RH', { name: 'RH', value: 0 });
    moduleMap.set('FINANCIAL', { name: 'Financeiro', value: 0 });
    moduleMap.set('SCHEDULING', { name: 'Agenda', value: 0 });
    moduleMap.set('BASIC', { name: 'Básico', value: 0 });

    // Preencher com dados reais
    moduleCounts.forEach(entry => {
      entry.modules.forEach(module => {
        if (moduleMap.has(module)) {
          const data = moduleMap.get(module);
          data.value += entry._count.id;
        }
      });
    });

    // Converter o mapa para um array
    const result = [];
    moduleMap.forEach(value => {
      result.push(value);
    });

    return result;
  }

  static async getActiveUsersData(req) {
    const { limit = 5 } = req.query;
    const userCompanyFilter = AdminDashboardController.getCompanyFilter(req);

    const users = await prisma.user.findMany({
      where: {
        ...userCompanyFilter,
        active: true,
      },
      select: {
        id: true,
        fullName: true,
        role: true,
        lastLoginAt: true,
      },
      orderBy: {
        lastLoginAt: 'desc',
      },
      take: Number(limit),
    });

    const twoHoursAgo = new Date();
    twoHoursAgo.setHours(twoHoursAgo.getHours() - 2);

    return users.map(user => ({
      id: user.id,
      name: user.fullName,
      role: formatUserRole(user.role),
      lastActive: user.lastLoginAt,
      status: user.lastLoginAt && user.lastLoginAt > twoHoursAgo ? 'online' : 'offline',
    }));
  }

  static async getRecentActivityData(req) {
    const { limit = 7 } = req.query;
    const userCompanyFilter = AdminDashboardController.getCompanyFilter(req);

    const auditLogs = await prisma.auditLog.findMany({
      where: {
        ...userCompanyFilter,
      },
      select: {
        id: true,
        action: true,
        entityType: true,
        entityId: true,
        details: true,
        createdAt: true,
        user: {
          select: {
            id: true,
            fullName: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: Number(limit),
    });

    return auditLogs.map(log => ({
      id: log.id,
      action: formatActionDescription(log.action, log.entityType),
      user: log.user ? log.user.fullName : 'Sistema',
      timestamp: log.createdAt,
      type: mapEntityTypeToActivityType(log.entityType),
    }));
  }

  static async getProfessionDistributionData(req) {
    // Para profissões e grupos, queremos ver todos os dados independentemente da empresa
    // quando o usuário for SYSTEM_ADMIN
    let where = {
      active: true,
      deletedAt: null,
    };

    // Se o usuário não for SYSTEM_ADMIN, aplicar filtro de empresa
    if (req.user.role !== "SYSTEM_ADMIN" && req.user.companyId) {
      where.companyId = req.user.companyId;
    }

    // Se for SYSTEM_ADMIN e um companyId específico for fornecido na query
    if (req.user.role === "SYSTEM_ADMIN" && req.query.companyId) {
      where.companyId = req.query.companyId;
    }

    // Buscar todos os grupos de profissões ativos
    const groups = await prisma.professionGroup.findMany({
      where,
      include: {
        professions: {
          where: {
            active: true,
            deletedAt: null
          },
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: {
        name: 'asc'
      }
    });

    // Formatar os dados para o gráfico
    const result = groups.map(group => ({
      name: group.name,
      value: group.professions.length,
      id: group.id
    })).filter(group => group.value > 0); // Filtrar apenas grupos com profissões

    // Adicionar uma entrada para profissões sem grupo
    const professionsWithoutGroup = await prisma.profession.count({
      where: {
        ...where,
        groupId: null,
      }
    });

    if (professionsWithoutGroup > 0) {
      result.push({
        name: 'Sem Grupo',
        value: professionsWithoutGroup,
        id: null
      });
    }

    // Se não houver grupos com profissões, retornar um array vazio
    if (result.length === 0) {
      return [];
    }

    return result;
  }

  static async getSystemInfoData(req) {
    const userCompanyFilter = AdminDashboardController.getCompanyFilter(req);

    // Obter versão do app a partir das variáveis de ambiente
    const appVersion = process.env.APP_VERSION || 'v0.1';

    // Obter informações do último backup através dos logs de auditoria
    const lastBackupLog = await prisma.auditLog.findFirst({
      where: {
        ...userCompanyFilter,
        action: 'BACKUP',
        entityType: 'SYSTEM',
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Verificar vulnerabilidades de segurança através dos logs
    const securityIssues = await prisma.auditLog.count({
      where: {
        ...userCompanyFilter,
        action: {
          in: ['SECURITY_ALERT', 'FAILED_LOGIN_ATTEMPT'],
        },
        createdAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Últimas 24 horas
        },
      },
    });

    // Determinar status de segurança com base nas issues encontradas
    const securityStatus = securityIssues === 0 ? 'Protegido' :
                          securityIssues < 3 ? 'Atenção Necessária' : 'Vulnerável';

    // Obter informações do plano da empresa se o filtro de empresa estiver ativo
    let currentPlan = 'Premium (Anual)'; // Valor padrão

    if (Object.keys(userCompanyFilter).length > 0) {
      const subscription = await prisma.subscription.findFirst({
        where: {
          companyId: userCompanyFilter.companyId,
          status: 'Active',
        },
        select: {
          plan: true,
          billingCycle: true,
        },
      });

      if (subscription) {
        const billingCycleText = subscription.billingCycle === 'MONTHLY' ? 'Mensal' :
                                subscription.billingCycle === 'YEARLY' ? 'Anual' :
                                subscription.billingCycle;

        currentPlan = `${subscription.plan} (${billingCycleText})`;
      }
    }

    return {
      version: appVersion,
      lastBackup: lastBackupLog ? lastBackupLog.createdAt : new Date(),
      securityStatus: securityStatus,
      currentPlan: currentPlan,
    };
  }

  /**
   * Obter dados do plano e assinatura para o dashboard de planos
   */
  static async getPlansData(req, res) {
    try {
      const userCompanyFilter = AdminDashboardController.getCompanyFilter(req);

      // Para system_admin, permitir especificar companyId via query parameter
      let targetCompanyId = userCompanyFilter.companyId;

      if (req.user.role === 'SYSTEM_ADMIN') {
        // System admin pode especificar qual empresa visualizar
        targetCompanyId = req.query.companyId || null;

        if (!targetCompanyId) {
          return res.status(400).json({
            message: 'System admin deve especificar o ID da empresa para visualizar dados do plano'
          });
        }
      } else if (!targetCompanyId) {
        return res.status(400).json({
          message: 'ID da empresa é obrigatório para visualizar dados do plano'
        });
      }

      // Buscar informações da assinatura
      const subscription = await prisma.subscription.findFirst({
        where: {
          companyId: targetCompanyId,
        },
        include: {
          modules: {
            where: {
              active: true,
            },
          },
          company: {
            select: {
              id: true,
              name: true,
              users: {
                where: {
                  active: true,
                },
                select: {
                  id: true,
                },
              },
            },
          },
        },
      });

      if (!subscription) {
        return res.status(404).json({
          message: 'Assinatura não encontrada para esta empresa'
        });
      }

      // Calcular estatísticas de uso
      const currentUserCount = subscription.company.users.length;
      const userLimitUsage = subscription.userLimit > 0
        ? Math.round((currentUserCount / subscription.userLimit) * 100)
        : 0;

      // Formatar dados da resposta
      const planData = {
        subscription: {
          id: subscription.id,
          status: subscription.status,
          billingCycle: subscription.billingCycle,
          pricePerMonth: parseFloat(subscription.pricePerMonth),
          userLimit: subscription.userLimit,
          startDate: subscription.startDate,
          endDate: subscription.endDate,
          nextBillingDate: subscription.nextBillingDate,
          cancelAtPeriodEnd: subscription.cancelAtPeriodEnd,
        },
        usage: {
          currentUsers: currentUserCount,
          userLimit: subscription.userLimit,
          userLimitUsage: userLimitUsage,
          availableUsers: Math.max(0, subscription.userLimit - currentUserCount),
        },
        modules: subscription.modules.map(module => ({
          id: module.id,
          moduleType: module.moduleType,
          pricePerMonth: parseFloat(module.pricePerMonth),
          addedAt: module.addedAt,
        })),
        company: {
          id: subscription.company.id,
          name: subscription.company.name,
        },
      };

      res.json(planData);
    } catch (error) {
      console.error('Erro ao obter dados do plano:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }
}

// Funções auxiliares
function formatUserRole(role) {
  const roleMap = {
    'SYSTEM_ADMIN': 'Administrador',
    'COMPANY_ADMIN': 'Administrador',
    'EMPLOYEE': 'Funcionário',
  };

  return roleMap[role] || role;
}

function formatActionDescription(action, entityType) {
  const actionMap = {
    'CREATE': `Criação de ${formatEntityType(entityType)}`,
    'UPDATE': `Atualização de ${formatEntityType(entityType)}`,
    'DELETE': `Exclusão de ${formatEntityType(entityType)}`,
    'LOGIN': 'Login no sistema',
    'LOGOUT': 'Logout do sistema',
    'BACKUP': 'Backup do sistema',
  };

  return actionMap[action] || action;
}

function formatEntityType(entityType) {
  const entityMap = {
    'USER': 'usuário',
    'CLIENT': 'cliente',
    'SCHEDULING': 'agendamento',
    'INSURANCE': 'convênio',
    'COMPANY': 'empresa',
    'LOCATION': 'local',
    'SERVICE_TYPE': 'tipo de serviço',
    'SYSTEM': 'sistema',
  };

  return entityMap[entityType] || entityType.toLowerCase();
}

function mapEntityTypeToActivityType(entityType) {
  const typeMap = {
    'USER': 'auth',
    'CLIENT': 'client',
    'SCHEDULING': 'scheduling',
    'SYSTEM': 'system',
    'COMPANY': 'settings',
    'LOCATION': 'settings',
    'SERVICE_TYPE': 'financial',
    'INSURANCE': 'financial',
  };

  return typeMap[entityType] || 'activity';
}

// Função para criar dados de exemplo para o dashboard
async function createSampleData() {
  try {
    console.log("Verificando se é necessário criar dados de exemplo...");

    // Verificar se já existem profissões
    const professionCount = await prisma.profession.count({
      where: {
        active: true,
        deletedAt: null
      }
    });

    // Verificar se já existem grupos
    const groupCount = await prisma.professionGroup.count({
      where: {
        active: true,
        deletedAt: null
      }
    });

    console.log(`Contagem atual: ${professionCount} profissões, ${groupCount} grupos`);

    // Se já existem dados, não criar novos
    if (professionCount > 0 && groupCount > 0) {
      console.log("Já existem dados no banco, não é necessário criar exemplos.");
      return;
    }

    console.log("Criando dados de exemplo para o dashboard...");

    // Definir grupos de exemplo
    const sampleGroups = [
      { name: 'Medicina', description: 'Profissionais médicos de diversas especialidades' },
      { name: 'Enfermagem', description: 'Profissionais de enfermagem' },
      { name: 'Fisioterapia', description: 'Profissionais de fisioterapia' },
      { name: 'Psicologia', description: 'Profissionais de psicologia' },
      { name: 'Nutrição', description: 'Profissionais de nutrição' },
      { name: 'Administrativo', description: 'Profissionais de suporte administrativo' }
    ];

    // Criar grupos
    const createdGroups = [];
    for (const groupData of sampleGroups) {
      const group = await prisma.professionGroup.create({
        data: {
          name: groupData.name,
          description: groupData.description,
          active: true
        }
      });
      createdGroups.push(group);
      console.log(`Grupo criado: ${group.name} (ID: ${group.id})`);
    }

    // Definir profissões de exemplo
    const sampleProfessions = [
      { name: 'Médico Clínico Geral', description: 'Atendimento médico geral', groupName: 'Medicina' },
      { name: 'Médico Cardiologista', description: 'Especialista em doenças cardiovasculares', groupName: 'Medicina' },
      { name: 'Médico Pediatra', description: 'Especialista em saúde infantil', groupName: 'Medicina' },
      { name: 'Enfermeiro', description: 'Cuidados de enfermagem', groupName: 'Enfermagem' },
      { name: 'Técnico de Enfermagem', description: 'Suporte em procedimentos de enfermagem', groupName: 'Enfermagem' },
      { name: 'Fisioterapeuta', description: 'Reabilitação física', groupName: 'Fisioterapia' },
      { name: 'Psicólogo', description: 'Atendimento psicológico', groupName: 'Psicologia' },
      { name: 'Nutricionista', description: 'Orientação nutricional', groupName: 'Nutrição' },
      { name: 'Recepcionista', description: 'Atendimento inicial', groupName: 'Administrativo' },
      { name: 'Assistente Administrativo', description: 'Suporte administrativo', groupName: 'Administrativo' }
    ];

    // Criar profissões
    for (const professionData of sampleProfessions) {
      const group = createdGroups.find(g => g.name === professionData.groupName);

      const profession = await prisma.profession.create({
        data: {
          name: professionData.name,
          description: professionData.description,
          groupId: group ? group.id : null,
          active: true
        }
      });
      console.log(`Profissão criada: ${profession.name} (ID: ${profession.id})`);
    }

    console.log("Dados de exemplo criados com sucesso!");
  } catch (error) {
    console.error("Erro ao criar dados de exemplo:", error);
  }
}

// Executar a criação de dados de exemplo ao iniciar o servidor
createSampleData();

module.exports = {
  AdminDashboardController,
};