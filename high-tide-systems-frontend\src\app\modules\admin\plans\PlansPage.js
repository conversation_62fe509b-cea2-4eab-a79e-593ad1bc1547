"use client";

import React, { useState, useEffect } from "react";
import {
  CreditCard,
  Users,
  Package,
  Calendar,
  DollarSign,
  AlertCircle,
  CheckCircle,
  XCircle,
  Plus,
  Minus,
  RefreshCw,
  Settings,
  Crown,
  Shield,
  Zap,
  Building
} from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { usePermissions } from "@/hooks/usePermissions";
import { plansService } from "@/app/modules/admin/services/plansService";
import { companyService } from "@/app/modules/admin/services/companyService";
import { ModuleHeader, ModuleSelect } from "@/components/ui";
import { toast_success, toast_error } from "@/components/ui/Toast";

const PlansPage = () => {
  const { user: currentUser } = useAuth();
  const { can } = usePermissions();
  const [planData, setPlanData] = useState(null);
  const [availablePlans, setAvailablePlans] = useState(null);
  const [companies, setCompanies] = useState([]);
  const [selectedCompanyId, setSelectedCompanyId] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingCompanies, setIsLoadingCompanies] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  // Verificar se o usuário atual é um system_admin
  const isSystemAdmin = currentUser?.role === "SYSTEM_ADMIN";

  // Função para carregar empresas (apenas para system_admin)
  const loadCompanies = async () => {
    if (!isSystemAdmin) return;

    setIsLoadingCompanies(true);
    try {
      const response = await companyService.getCompaniesForSelect();
      setCompanies(response);
      
      // Se não há empresa selecionada e há empresas disponíveis, selecionar a primeira
      if (!selectedCompanyId && response.length > 0) {
        setSelectedCompanyId(response[0].id);
      }
    } catch (error) {
      console.error("Erro ao carregar empresas:", error);
      toast_error({
        title: "Erro",
        message: "Não foi possível carregar as empresas."
      });
    } finally {
      setIsLoadingCompanies(false);
    }
  };

  // Função para carregar dados do plano
  const loadPlanData = async () => {
    setIsLoading(true);
    try {
      const companyId = isSystemAdmin ? selectedCompanyId : null;
      const [planResponse, availablePlansResponse] = await Promise.all([
        plansService.getPlansData(companyId),
        plansService.getAvailablePlans()
      ]);
      
      setPlanData(planResponse);
      setAvailablePlans(availablePlansResponse);
    } catch (error) {
      console.error("Erro ao carregar dados do plano:", error);
      toast_error({
        title: "Erro",
        message: "Não foi possível carregar os dados do plano."
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Carregar dados iniciais
  useEffect(() => {
    if (isSystemAdmin) {
      loadCompanies();
    } else {
      loadPlanData();
    }
  }, [isSystemAdmin]);

  // Recarregar dados quando a empresa selecionada mudar
  useEffect(() => {
    if (isSystemAdmin && selectedCompanyId) {
      loadPlanData();
    } else if (!isSystemAdmin) {
      loadPlanData();
    }
  }, [selectedCompanyId, isSystemAdmin]);

  // Função para adicionar usuários
  const handleAddUsers = async (additionalUsers) => {
    setIsUpdating(true);
    try {
      await plansService.addUsers(additionalUsers);
      toast_success({
        title: "Sucesso",
        message: `${additionalUsers} usuário(s) adicionado(s) ao plano.`
      });
      loadPlanData();
    } catch (error) {
      console.error("Erro ao adicionar usuários:", error);
      toast_error({
        title: "Erro",
        message: "Não foi possível adicionar usuários ao plano."
      });
    } finally {
      setIsUpdating(false);
    }
  };

  // Função para adicionar módulo
  const handleAddModule = async (moduleType) => {
    setIsUpdating(true);
    try {
      await plansService.addModule(moduleType);
      toast_success({
        title: "Sucesso",
        message: "Módulo adicionado ao plano com sucesso."
      });
      loadPlanData();
    } catch (error) {
      console.error("Erro ao adicionar módulo:", error);
      toast_error({
        title: "Erro",
        message: "Não foi possível adicionar o módulo ao plano."
      });
    } finally {
      setIsUpdating(false);
    }
  };

  // Função para remover módulo
  const handleRemoveModule = async (moduleType) => {
    setIsUpdating(true);
    try {
      await plansService.removeModule(moduleType);
      toast_success({
        title: "Sucesso",
        message: "Módulo removido do plano com sucesso."
      });
      loadPlanData();
    } catch (error) {
      console.error("Erro ao remover módulo:", error);
      toast_error({
        title: "Erro",
        message: "Não foi possível remover o módulo do plano."
      });
    } finally {
      setIsUpdating(false);
    }
  };

  // Função para cancelar assinatura
  const handleCancelSubscription = async () => {
    if (!confirm("Tem certeza que deseja cancelar a assinatura?")) return;
    
    setIsUpdating(true);
    try {
      await plansService.cancelSubscription();
      toast_success({
        title: "Sucesso",
        message: "Assinatura cancelada com sucesso."
      });
      loadPlanData();
    } catch (error) {
      console.error("Erro ao cancelar assinatura:", error);
      toast_error({
        title: "Erro",
        message: "Não foi possível cancelar a assinatura."
      });
    } finally {
      setIsUpdating(false);
    }
  };

  // Função para reativar assinatura
  const handleReactivateSubscription = async () => {
    setIsUpdating(true);
    try {
      await plansService.reactivateSubscription();
      toast_success({
        title: "Sucesso",
        message: "Assinatura reativada com sucesso."
      });
      loadPlanData();
    } catch (error) {
      console.error("Erro ao reativar assinatura:", error);
      toast_error({
        title: "Erro",
        message: "Não foi possível reativar a assinatura."
      });
    } finally {
      setIsUpdating(false);
    }
  };

  // Função para formatar status
  const getStatusInfo = (status) => {
    switch (status) {
      case 'ACTIVE':
        return { 
          label: 'Ativo', 
          color: 'text-green-600 dark:text-green-400', 
          bgColor: 'bg-green-100 dark:bg-green-900/30',
          icon: CheckCircle 
        };
      case 'CANCELED':
        return { 
          label: 'Cancelado', 
          color: 'text-red-600 dark:text-red-400', 
          bgColor: 'bg-red-100 dark:bg-red-900/30',
          icon: XCircle 
        };
      case 'PAST_DUE':
        return { 
          label: 'Em Atraso', 
          color: 'text-yellow-600 dark:text-yellow-400', 
          bgColor: 'bg-yellow-100 dark:bg-yellow-900/30',
          icon: AlertCircle 
        };
      default:
        return { 
          label: status, 
          color: 'text-gray-600 dark:text-gray-400', 
          bgColor: 'bg-gray-100 dark:bg-gray-900/30',
          icon: AlertCircle 
        };
    }
  };

  // Função para formatar ciclo de cobrança
  const getBillingCycleLabel = (cycle) => {
    switch (cycle) {
      case 'MONTHLY':
        return 'Mensal';
      case 'YEARLY':
        return 'Anual';
      default:
        return cycle;
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="animate-spin h-8 w-8 text-gray-400" />
        <span className="ml-2 text-gray-600 dark:text-gray-400">Carregando dados do plano...</span>
      </div>
    );
  }

  // Mostrar mensagem para system_admin quando nenhuma empresa está selecionada
  if (isSystemAdmin && !selectedCompanyId) {
    return (
      <div className="space-y-6">
        <ModuleHeader
          title="Gerenciamento de Planos"
          icon={<CreditCard size={22} className="text-module-admin-icon dark:text-module-admin-icon-dark" />}
          description="Gerencie planos, usuários e módulos das assinaturas das empresas."
          moduleColor="admin"
          filters={
            <div className="w-full sm:w-64">
              <ModuleSelect
                moduleColor="admin"
                value={selectedCompanyId}
                onChange={(e) => setSelectedCompanyId(e.target.value)}
                placeholder="Selecione uma empresa"
                disabled={isLoadingCompanies}
              >
                <option value="">Selecione uma empresa</option>
                {companies.map((company) => (
                  <option key={company.id} value={company.id}>
                    {company.name}
                  </option>
                ))}
              </ModuleSelect>
            </div>
          }
        />

        <div className="text-center py-12">
          <Building className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">
            Selecione uma empresa
          </h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Escolha uma empresa no seletor acima para visualizar e gerenciar seu plano.
          </p>
        </div>
      </div>
    );
  }

  if (!planData) {
    return (
      <div className="space-y-6">
        <ModuleHeader
          title="Gerenciamento de Planos"
          icon={<CreditCard size={22} className="text-module-admin-icon dark:text-module-admin-icon-dark" />}
          description="Gerencie seu plano, usuários e módulos da assinatura."
          moduleColor="admin"
          filters={
            isSystemAdmin && (
              <div className="w-full sm:w-64">
                <ModuleSelect
                  moduleColor="admin"
                  value={selectedCompanyId}
                  onChange={(e) => setSelectedCompanyId(e.target.value)}
                  placeholder="Selecione uma empresa"
                  disabled={isLoadingCompanies}
                >
                  <option value="">Selecione uma empresa</option>
                  {companies.map((company) => (
                    <option key={company.id} value={company.id}>
                      {company.name}
                    </option>
                  ))}
                </ModuleSelect>
              </div>
            )
          }
        />

        <div className="text-center py-12">
          <AlertCircle className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">
            Nenhum plano encontrado
          </h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Não foi possível encontrar informações do plano para esta empresa.
          </p>
        </div>
      </div>
    );
  }

  const statusInfo = getStatusInfo(planData.subscription.status);
  const StatusIcon = statusInfo.icon;

  return (
    <div className="space-y-6">
      {/* Cabeçalho */}
      <ModuleHeader
        title="Gerenciamento de Planos"
        icon={<CreditCard size={22} className="text-module-admin-icon dark:text-module-admin-icon-dark" />}
        description={isSystemAdmin
          ? `Gerencie o plano, usuários e módulos da assinatura de ${planData.company.name}.`
          : "Gerencie seu plano, usuários e módulos da assinatura."
        }
        moduleColor="admin"
        filters={
          isSystemAdmin && (
            <div className="w-full sm:w-64">
              <ModuleSelect
                moduleColor="admin"
                value={selectedCompanyId}
                onChange={(e) => setSelectedCompanyId(e.target.value)}
                placeholder="Selecione uma empresa"
                disabled={isLoadingCompanies}
              >
                <option value="">Selecione uma empresa</option>
                {companies.map((company) => (
                  <option key={company.id} value={company.id}>
                    {company.name}
                  </option>
                ))}
              </ModuleSelect>
            </div>
          )
        }
      />

      {/* Cards principais */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Card do Plano Atual */}
        <div className="lg:col-span-2 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center">
              <Crown className="mr-2 h-5 w-5 text-yellow-500" />
              Plano Atual
            </h2>
            <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusInfo.bgColor} ${statusInfo.color}`}>
              <StatusIcon className="mr-1 h-3 w-3" />
              {statusInfo.label}
            </div>
          </div>

          <div className="space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">Empresa</p>
                <p className="text-base font-medium text-gray-900 dark:text-gray-100">
                  {planData.company.name}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">Ciclo de Cobrança</p>
                <p className="text-base font-medium text-gray-900 dark:text-gray-100">
                  {getBillingCycleLabel(planData.subscription.billingCycle)}
                </p>
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">Preço Mensal</p>
                <p className="text-xl font-bold text-gray-900 dark:text-gray-100">
                  R$ {planData.subscription.pricePerMonth.toFixed(2)}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">Próxima Cobrança</p>
                <p className="text-base font-medium text-gray-900 dark:text-gray-100">
                  {planData.subscription.nextBillingDate
                    ? new Date(planData.subscription.nextBillingDate).toLocaleDateString('pt-BR')
                    : 'N/A'
                  }
                </p>
              </div>
            </div>

            {/* Ações do plano */}
            <div className="flex flex-wrap gap-2 pt-4 border-t border-gray-200 dark:border-gray-700">
              {planData.subscription.status === 'ACTIVE' ? (
                <button
                  onClick={handleCancelSubscription}
                  disabled={isUpdating}
                  className="flex items-center px-3 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-md transition-colors disabled:opacity-50"
                >
                  <XCircle className="mr-1 h-4 w-4" />
                  Cancelar Plano
                </button>
              ) : (
                <button
                  onClick={handleReactivateSubscription}
                  disabled={isUpdating}
                  className="flex items-center px-3 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-md transition-colors disabled:opacity-50"
                >
                  <CheckCircle className="mr-1 h-4 w-4" />
                  Reativar Plano
                </button>
              )}

              <button
                onClick={() => window.open('/subscription/invoices', '_blank')}
                className="flex items-center px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-md transition-colors"
              >
                <Calendar className="mr-1 h-4 w-4" />
                Ver Faturas
              </button>
            </div>
          </div>
        </div>

        {/* Card de Uso de Usuários */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center mb-4">
            <Users className="mr-2 h-5 w-5 text-blue-500" />
            Usuários
          </h3>

          <div className="space-y-4">
            <div>
              <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
                <span>Uso atual</span>
                <span>{planData.usage.currentUsers} / {planData.usage.userLimit}</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${Math.min(planData.usage.userLimitUsage, 100)}%` }}
                ></div>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                {planData.usage.userLimitUsage}% utilizado
              </p>
            </div>

            <div className="text-center">
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                {planData.usage.availableUsers} usuários disponíveis
              </p>
              <button
                onClick={() => handleAddUsers(5)}
                disabled={isUpdating}
                className="w-full flex items-center justify-center px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors disabled:opacity-50"
              >
                <Plus className="mr-1 h-4 w-4" />
                Adicionar 5 usuários
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Seção de Módulos */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center mb-6">
          <Package className="mr-2 h-5 w-5 text-purple-500" />
          Módulos da Assinatura
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {/* Módulos Ativos */}
          {planData.modules.map((module) => (
            <div key={module.id} className="border border-green-200 dark:border-green-800 rounded-lg p-4 bg-green-50 dark:bg-green-900/20">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                  <span className="font-medium text-gray-900 dark:text-gray-100">
                    {getModuleName(module.moduleType)}
                  </span>
                </div>
                <span className="text-sm text-green-600 dark:text-green-400 font-medium">
                  Ativo
                </span>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                R$ {module.pricePerMonth.toFixed(2)}/mês
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Adicionado em {new Date(module.addedAt).toLocaleDateString('pt-BR')}
              </p>

              {/* Botão para remover módulo (apenas módulos opcionais) */}
              {!isBasicModule(module.moduleType) && (
                <button
                  onClick={() => handleRemoveModule(module.moduleType)}
                  disabled={isUpdating}
                  className="mt-3 w-full flex items-center justify-center px-2 py-1 bg-red-100 hover:bg-red-200 dark:bg-red-900/30 dark:hover:bg-red-900/50 text-red-700 dark:text-red-400 text-xs font-medium rounded transition-colors disabled:opacity-50"
                >
                  <Minus className="mr-1 h-3 w-3" />
                  Remover
                </button>
              )}
            </div>
          ))}

          {/* Módulos Disponíveis */}
          {availablePlans && Object.entries(availablePlans.modules)
            .filter(([moduleType, moduleInfo]) =>
              !planData.modules.some(m => m.moduleType === moduleType) &&
              !moduleInfo.included
            )
            .map(([moduleType, moduleInfo]) => (
              <div key={moduleType} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-gray-50 dark:bg-gray-900/20">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center">
                    <Package className="h-5 w-5 text-gray-400 mr-2" />
                    <span className="font-medium text-gray-900 dark:text-gray-100">
                      {moduleInfo.name}
                    </span>
                  </div>
                  <span className="text-sm text-gray-500 dark:text-gray-400 font-medium">
                    Disponível
                  </span>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                  {moduleInfo.description}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  R$ {moduleInfo.monthlyPrice.toFixed(2)}/mês
                </p>

                <button
                  onClick={() => handleAddModule(moduleType)}
                  disabled={isUpdating}
                  className="w-full flex items-center justify-center px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium rounded transition-colors disabled:opacity-50"
                >
                  <Plus className="mr-1 h-3 w-3" />
                  Adicionar
                </button>
              </div>
            ))}
        </div>
      </div>
    </div>
  );
};

// Função auxiliar para obter nome do módulo
const getModuleName = (moduleType) => {
  const moduleNames = {
    'BASIC': 'Módulo Básico',
    'ADMIN': 'Administração',
    'SCHEDULING': 'Agendamento',
    'PEOPLE': 'Pessoas',
    'REPORTS': 'Relatórios',
    'CHAT': 'Chat',
    'ABAPLUS': 'ABA+'
  };
  return moduleNames[moduleType] || moduleType;
};

// Função auxiliar para verificar se é módulo básico
const isBasicModule = (moduleType) => {
  return ['BASIC', 'ADMIN', 'SCHEDULING'].includes(moduleType);
};

export default PlansPage;
