// services/plansService.js
import { api } from '@/utils/api';

export const plansService = {
  /**
   * Obtém dados do plano atual da empresa
   */
  async getPlansData(companyId = null) {
    try {
      const params = companyId ? { companyId } : {};
      const response = await api.get('/adminDashboard/plans', { params });
      return response.data;
    } catch (error) {
      console.error('Erro ao buscar dados do plano:', error);
      throw error;
    }
  },

  /**
   * Obtém informações da assinatura atual
   */
  async getSubscription() {
    try {
      const response = await api.get('/subscription/subscription');
      return response.data;
    } catch (error) {
      console.error('Erro ao buscar assinatura:', error);
      throw error;
    }
  },

  /**
   * Obtém planos disponíveis
   */
  async getAvailablePlans() {
    try {
      const response = await api.get('/subscription/plans');
      return response.data;
    } catch (error) {
      console.error('Erro ao buscar planos disponíveis:', error);
      throw error;
    }
  },

  /**
   * Adiciona usuários ao plano
   */
  async addUsers(additionalUsers) {
    try {
      const response = await api.post('/subscription/users/add', {
        additionalUsers
      });
      return response.data;
    } catch (error) {
      console.error('Erro ao adicionar usuários:', error);
      throw error;
    }
  },

  /**
   * Adiciona um módulo à assinatura
   */
  async addModule(moduleType) {
    try {
      const response = await api.post('/subscription/module/add', {
        moduleType
      });
      return response.data;
    } catch (error) {
      console.error('Erro ao adicionar módulo:', error);
      throw error;
    }
  },

  /**
   * Remove um módulo da assinatura
   */
  async removeModule(moduleType) {
    try {
      const response = await api.post('/subscription/module/remove', {
        moduleType
      });
      return response.data;
    } catch (error) {
      console.error('Erro ao remover módulo:', error);
      throw error;
    }
  },

  /**
   * Cancela a assinatura
   */
  async cancelSubscription() {
    try {
      const response = await api.post('/subscription/cancel');
      return response.data;
    } catch (error) {
      console.error('Erro ao cancelar assinatura:', error);
      throw error;
    }
  },

  /**
   * Reativa a assinatura
   */
  async reactivateSubscription() {
    try {
      const response = await api.post('/subscription/reactivate');
      return response.data;
    } catch (error) {
      console.error('Erro ao reativar assinatura:', error);
      throw error;
    }
  },

  /**
   * Faz upgrade do plano
   */
  async upgradePlan(planType, userLimit) {
    try {
      const response = await api.post('/subscription/upgrade', {
        planType,
        userLimit
      });
      return response.data;
    } catch (error) {
      console.error('Erro ao fazer upgrade do plano:', error);
      throw error;
    }
  },

  /**
   * Obtém faturas
   */
  async getInvoices() {
    try {
      const response = await api.get('/subscription/invoices');
      return response.data;
    } catch (error) {
      console.error('Erro ao buscar faturas:', error);
      throw error;
    }
  },

  /**
   * Cria sessão de checkout
   */
  async createCheckoutSession(billingCycle = 'monthly') {
    try {
      const response = await api.post('/subscription/checkout', {
        billingCycle
      });
      return response.data;
    } catch (error) {
      console.error('Erro ao criar sessão de checkout:', error);
      throw error;
    }
  }
};
